using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.DependencyInjection;

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// ModelTrainingView.xaml 的交互逻辑
    /// </summary>
    public partial class ModelTrainingView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<ModelTrainingView> _logger;
        private readonly ObservableCollection<TrainedModel> _trainedModels;
        private readonly IYoloService? _yoloService;
        private readonly INnUNetService? _nnunetService;
        private bool _isTraining = false;
        private CancellationTokenSource? _trainingCancellationTokenSource;
        private string _currentTrainingMethod = "YOLO";

        // 数据集名称到路径的映射
        private readonly Dictionary<string, string> _datasetPaths = new Dictionary<string, string>
        {
            ["医学影像数据集 v1.0"] = @".\data\medical_dataset_v1",
            ["DICOM标注数据集"] = @".\data\dicom_annotated_dataset",
            ["自定义数据集"] = @".\data\custom_dataset",
            ["nnUNet医学分割数据集"] = @".\data\nnUNet_raw\Dataset001_Medical",
            ["脑部MRI数据集"] = @".\data\nnUNet_raw\Dataset002_BrainMRI",
            ["肺部CT数据集"] = @".\data\nnUNet_raw\Dataset003_LungCT"
        };

        public ModelTrainingView()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<ModelTrainingView>.Instance;
            _trainedModels = new ObservableCollection<TrainedModel>();
            TrainedModelsListView.ItemsSource = _trainedModels;

            // 尝试获取服务（如果可用）
            try
            {
                if (App.Current is App app && app.ServiceProvider != null)
                {
                    _yoloService = app.ServiceProvider.GetService<IYoloService>();
                    _nnunetService = app.ServiceProvider.GetService<INnUNetService>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "无法获取训练服务");
            }

            // 初始化示例数据
            InitializeSampleData();

            // 添加Loaded事件处理程序
            this.Loaded += ModelTrainingView_Loaded;
        }

        /// <summary>
        /// 页面加载完成事件处理
        /// </summary>
        private void ModelTrainingView_Loaded(object sender, RoutedEventArgs e)
        {
            // 确保初始状态正确设置
            if (TrainingMethodComboBox.SelectedIndex == -1)
            {
                TrainingMethodComboBox.SelectedIndex = 0; // 默认选择YOLO
            }

            // 手动触发一次选择变化以确保UI状态正确
            TrainingMethodComboBox_SelectionChanged(TrainingMethodComboBox,
                new SelectionChangedEventArgs(ComboBox.SelectionChangedEvent, new List<object>(), new List<object>()));
        }

        /// <summary>
        /// 初始化示例数据
        /// </summary>
        private void InitializeSampleData()
        {
            // 添加一些示例模型
            _trainedModels.Add(new TrainedModel
            {
                Name = "YOLOv11_Medical_v1.0",
                Accuracy = 89.5,
                CreatedTime = DateTime.Now.AddDays(-5),
                ModelPath = "./models/yolov11_medical_v1.pt"
            });

            _trainedModels.Add(new TrainedModel
            {
                Name = "YOLOv11_Fracture_v2.1",
                Accuracy = 92.3,
                CreatedTime = DateTime.Now.AddDays(-2),
                ModelPath = "./models/yolov11_fracture_v2.pt"
            });
        }

        /// <summary>
        /// 数据集选择变化
        /// </summary>
        private void DatasetComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DatasetComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var datasetName = selectedItem.Content.ToString();
                UpdateDatasetInfo(datasetName);
            }
        }

        /// <summary>
        /// 训练方法选择变化事件处理
        /// </summary>
        private void TrainingMethodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 检查是否所有UI元素都已初始化
            if (TrainingMethodComboBox?.SelectedItem is ComboBoxItem selectedItem &&
                YoloModelPanel != null && NnUNetModelPanel != null &&
                YoloTrainingParams != null && NnUNetTrainingParams != null &&
                YoloAdvancedSettings != null && NnUNetAdvancedSettings != null)
            {
                var method = selectedItem.Tag?.ToString() ?? "YOLO";
                _currentTrainingMethod = method;
                _logger.LogInformation("选择训练方法: {Method}", method);

                // 根据选择的方法显示/隐藏相应的UI元素
                if (method == "nnUNet")
                {
                    // 显示nnUNet相关UI
                    YoloModelPanel.Visibility = Visibility.Collapsed;
                    NnUNetModelPanel.Visibility = Visibility.Visible;
                    YoloTrainingParams.Visibility = Visibility.Collapsed;
                    NnUNetTrainingParams.Visibility = Visibility.Visible;
                    YoloAdvancedSettings.Visibility = Visibility.Collapsed;
                    NnUNetAdvancedSettings.Visibility = Visibility.Visible;
                }
                else
                {
                    // 显示YOLO相关UI
                    YoloModelPanel.Visibility = Visibility.Visible;
                    NnUNetModelPanel.Visibility = Visibility.Collapsed;
                    YoloTrainingParams.Visibility = Visibility.Visible;
                    NnUNetTrainingParams.Visibility = Visibility.Collapsed;
                    YoloAdvancedSettings.Visibility = Visibility.Visible;
                    NnUNetAdvancedSettings.Visibility = Visibility.Collapsed;
                }
            }
        }

        /// <summary>
        /// 更新数据集信息
        /// </summary>
        private void UpdateDatasetInfo(string? datasetName)
        {
            // 模拟数据集信息
            switch (datasetName)
            {
                case "医学影像数据集 v1.0":
                    DatasetImageCountText.Text = "图像数量: 2,500";
                    DatasetAnnotationCountText.Text = "标注数量: 8,750";
                    DatasetSizeText.Text = "数据集大小: 1.2 GB";
                    DatasetClassesText.Text = "类别数量: 5";
                    break;
                case "DICOM标注数据集":
                    DatasetImageCountText.Text = "图像数量: 1,800";
                    DatasetAnnotationCountText.Text = "标注数量: 6,200";
                    DatasetSizeText.Text = "数据集大小: 950 MB";
                    DatasetClassesText.Text = "类别数量: 3";
                    break;
                case "自定义数据集":
                    DatasetImageCountText.Text = "图像数量: 待加载";
                    DatasetAnnotationCountText.Text = "标注数量: 待加载";
                    DatasetSizeText.Text = "数据集大小: 待加载";
                    DatasetClassesText.Text = "类别数量: 待加载";
                    break;
                default:
                    DatasetImageCountText.Text = "图像数量: 未选择";
                    DatasetAnnotationCountText.Text = "标注数量: 未选择";
                    DatasetSizeText.Text = "数据集大小: 未选择";
                    DatasetClassesText.Text = "类别数量: 未选择";
                    break;
            }
        }

        /// <summary>
        /// 浏览数据集
        /// </summary>
        private void BrowseDataset_Click(object sender, RoutedEventArgs e)
        {
            // 使用简单的输入对话框代替文件夹浏览器
            var folderPath = Microsoft.VisualBasic.Interaction.InputBox(
                "请输入数据集目录路径:",
                "选择数据集目录",
                @"C:\");

            if (!string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath))
            {
                try
                {
                    ValidateDatasetStructure(folderPath);
                    WpfMessageBox.Show($"数据集路径已设置：{folderPath}", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "浏览数据集时发生错误");
                    WpfMessageBox.Show($"设置数据集路径失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 预览数据集
        /// </summary>
        private void PreviewDataset_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前选择的数据集
                string? selectedDatasetName = null;
                string? datasetPath = null;

                if (DatasetComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    selectedDatasetName = selectedItem.Content?.ToString();
                }

                // 如果没有选择数据集，提示用户
                if (string.IsNullOrEmpty(selectedDatasetName))
                {
                    WpfMessageBox.Show("请先选择一个数据集。", "提示",
                                     MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 从映射中获取数据集路径
                if (_datasetPaths.TryGetValue(selectedDatasetName, out datasetPath))
                {
                    // 转换为绝对路径
                    datasetPath = Path.GetFullPath(datasetPath);
                }
                else
                {
                    // 如果映射中没有，尝试从常见位置查找
                    var commonPaths = new[]
                    {
                        Path.Combine(Environment.CurrentDirectory, "data", selectedDatasetName),
                        Path.Combine(Environment.CurrentDirectory, "datasets", selectedDatasetName),
                        Path.Combine(@"C:\Data", selectedDatasetName),
                        Path.Combine(@"D:\Data", selectedDatasetName)
                    };

                    foreach (var path in commonPaths)
                    {
                        if (Directory.Exists(path))
                        {
                            datasetPath = path;
                            break;
                        }
                    }
                }

                // 检查路径是否存在
                if (string.IsNullOrEmpty(datasetPath) || !Directory.Exists(datasetPath))
                {
                    // 如果路径不存在，询问用户是否要创建示例结构
                    var result = WpfMessageBox.Show(
                        $"数据集路径不存在：{datasetPath ?? "未知路径"}\n\n是否要创建示例数据集结构？",
                        "数据集不存在",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        CreateSampleDatasetStructure(selectedDatasetName);
                    }
                    return;
                }

                // 打开文件夹
                OpenFolderInExplorer(datasetPath);

                _logger.LogInformation("已打开数据集文件夹: {DatasetPath}", datasetPath);

                // 显示数据集信息
                ShowDatasetInfo(datasetPath, selectedDatasetName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览数据集时发生错误");
                WpfMessageBox.Show($"预览数据集失败：{ex.Message}", "错误",
                                 MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在资源管理器中打开文件夹
        /// </summary>
        private void OpenFolderInExplorer(string folderPath)
        {
            try
            {
                if (Directory.Exists(folderPath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = "explorer.exe",
                        Arguments = $"\"{folderPath}\"",
                        UseShellExecute = true
                    });
                }
                else
                {
                    throw new DirectoryNotFoundException($"文件夹不存在: {folderPath}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件夹失败: {FolderPath}", folderPath);
                throw;
            }
        }

        /// <summary>
        /// 显示数据集详细信息
        /// </summary>
        private void ShowDatasetInfo(string datasetPath, string datasetName)
        {
            try
            {
                var info = AnalyzeDatasetStructure(datasetPath);

                var message = $"数据集: {datasetName}\n" +
                             $"路径: {datasetPath}\n\n" +
                             $"结构分析:\n" +
                             $"• 图像文件: {info.ImageCount} 个\n" +
                             $"• 标注文件: {info.LabelCount} 个\n" +
                             $"• 子文件夹: {info.SubfolderCount} 个\n" +
                             $"• 总大小: {FormatFileSize(info.TotalSize)}\n\n" +
                             $"文件夹已在资源管理器中打开。";

                WpfMessageBox.Show(message, "数据集信息",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "分析数据集结构时发生错误");
                WpfMessageBox.Show($"文件夹已打开，但无法分析数据集结构：{ex.Message}",
                                  "部分成功", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 分析数据集结构
        /// </summary>
        private DatasetInfo AnalyzeDatasetStructure(string datasetPath)
        {
            var info = new DatasetInfo();

            if (!Directory.Exists(datasetPath))
                return info;

            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".dcm", ".nii", ".nii.gz" };
            var labelExtensions = new[] { ".txt", ".xml", ".json", ".csv" };

            foreach (var file in Directory.GetFiles(datasetPath, "*", SearchOption.AllDirectories))
            {
                var extension = Path.GetExtension(file).ToLower();
                var fileInfo = new System.IO.FileInfo(file);

                info.TotalSize += fileInfo.Length;

                if (imageExtensions.Contains(extension))
                {
                    info.ImageCount++;
                }
                else if (labelExtensions.Contains(extension))
                {
                    info.LabelCount++;
                }
            }

            info.SubfolderCount = Directory.GetDirectories(datasetPath, "*", SearchOption.AllDirectories).Length;

            return info;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 创建示例数据集结构
        /// </summary>
        private void CreateSampleDatasetStructure(string datasetName)
        {
            try
            {
                var basePath = Path.Combine(Environment.CurrentDirectory, "data");
                var datasetPath = Path.Combine(basePath, datasetName.Replace(" ", "_"));

                // 创建基本目录结构
                Directory.CreateDirectory(datasetPath);
                Directory.CreateDirectory(Path.Combine(datasetPath, "images", "train"));
                Directory.CreateDirectory(Path.Combine(datasetPath, "images", "val"));
                Directory.CreateDirectory(Path.Combine(datasetPath, "images", "test"));
                Directory.CreateDirectory(Path.Combine(datasetPath, "labels", "train"));
                Directory.CreateDirectory(Path.Combine(datasetPath, "labels", "val"));

                // 创建说明文件
                var readmePath = Path.Combine(datasetPath, "README.md");
                var readmeContent = $@"# {datasetName}

## 数据集结构

```
{datasetName}/
├── images/
│   ├── train/          # 训练图像
│   ├── val/            # 验证图像
│   └── test/           # 测试图像
├── labels/
│   ├── train/          # 训练标注
│   └── val/            # 验证标注
└── README.md           # 本文件

```

## 使用说明

1. 将训练图像放入 `images/train/` 目录
2. 将对应的标注文件放入 `labels/train/` 目录
3. 将验证图像放入 `images/val/` 目录
4. 将对应的标注文件放入 `labels/val/` 目录
5. 测试图像放入 `images/test/` 目录（可选）

## 支持的格式

- 图像格式: .jpg, .jpeg, .png, .bmp, .tiff, .dcm, .nii.gz
- 标注格式: .txt (YOLO), .xml (Pascal VOC), .json (COCO)

创建时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
";

                File.WriteAllText(readmePath, readmeContent);

                // 更新数据集路径映射
                _datasetPaths[datasetName] = datasetPath;

                WpfMessageBox.Show($"示例数据集结构已创建：\n{datasetPath}\n\n请将您的数据文件放入相应目录中。",
                                  "创建成功", MessageBoxButton.OK, MessageBoxImage.Information);

                // 打开创建的文件夹
                OpenFolderInExplorer(datasetPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建示例数据集结构失败");
                WpfMessageBox.Show($"创建示例数据集结构失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 验证数据集结构
        /// </summary>
        private void ValidateDatasetStructure(string datasetPath)
        {
            // 简化的数据集结构验证
            var requiredFolders = new[] { "images", "labels" };
            var missingFolders = new List<string>();

            foreach (var folder in requiredFolders)
            {
                var folderPath = Path.Combine(datasetPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    missingFolders.Add(folder);
                }
            }

            if (missingFolders.Any())
            {
                throw new DirectoryNotFoundException($"缺少必要的文件夹: {string.Join(", ", missingFolders)}");
            }
        }

        /// <summary>
        /// 验证数据集
        /// </summary>
        private async void ValidateDataset_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateTrainingStatus("正在验证数据集...", Brushes.Orange);

                // 模拟验证过程
                await Task.Delay(3000);

                var result = MessageBox.Show("数据集验证完成！\n\n发现问题：\n• 3个图像文件缺少对应标注\n• 2个标注文件格式不正确\n\n是否自动修复这些问题？",
                                           "验证结果", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdateTrainingStatus("正在修复数据集问题...", Brushes.Orange);
                    await Task.Delay(2000);
                    UpdateTrainingStatus("数据集验证和修复完成", Brushes.Green);
                }
                else
                {
                    UpdateTrainingStatus("数据集验证完成（存在问题）", Brushes.Orange);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证数据集时发生错误");
                MessageBox.Show($"验证数据集失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("数据集验证失败", Brushes.Red);
            }
        }

        /// <summary>
        /// 数据增强
        /// </summary>
        private async void DataAugmentation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("数据增强将生成额外的训练样本，这可能需要较长时间。是否继续？",
                                           "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdateTrainingStatus("正在进行数据增强...", Brushes.Orange);

                    // 模拟数据增强过程
                    await Task.Delay(5000);

                    UpdateTrainingStatus("数据增强完成", Brushes.Green);
                    MessageBox.Show("数据增强完成！\n\n生成了1,200个新的训练样本。", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);

                    // 更新数据集信息
                    DatasetImageCountText.Text = "图像数量: 3,700";
                    DatasetAnnotationCountText.Text = "标注数量: 12,950";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据增强时发生错误");
                MessageBox.Show($"数据增强失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("数据增强失败", Brushes.Red);
            }
        }

        /// <summary>
        /// 开始训练
        /// </summary>
        private async void StartTraining_Click(object sender, RoutedEventArgs e)
        {
            if (_isTraining)
            {
                MessageBox.Show("训练正在进行中，请等待完成或先停止当前训练。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 验证训练参数
            if (DatasetComboBox.SelectedItem == null)
            {
                MessageBox.Show("请先选择训练数据集。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                _isTraining = true;
                _trainingCancellationTokenSource = new CancellationTokenSource();

                // 更新UI状态
                StartTrainingButton.IsEnabled = false;
                PauseTrainingButton.IsEnabled = true;
                StopTrainingButton.IsEnabled = true;
                TrainingProgressCard.Visibility = Visibility.Visible;

                UpdateTrainingStatus("正在初始化训练...", Brushes.Orange);

                // 根据选择的训练方法执行不同的训练流程
                if (_currentTrainingMethod == "nnUNet")
                {
                    await StartNnUNetTraining(_trainingCancellationTokenSource.Token);
                }
                else
                {
                    await StartYoloTraining(_trainingCancellationTokenSource.Token);
                }

                // 训练完成的处理在各自的训练方法中进行
            }
            catch (OperationCanceledException)
            {
                UpdateTrainingStatus("训练已取消", Brushes.Gray);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "训练过程中发生错误");
                MessageBox.Show($"训练失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateTrainingStatus("训练失败", Brushes.Red);
            }
            finally
            {
                // 重置UI状态
                _isTraining = false;
                StartTrainingButton.IsEnabled = true;
                PauseTrainingButton.IsEnabled = false;
                StopTrainingButton.IsEnabled = false;
                _trainingCancellationTokenSource?.Dispose();
                _trainingCancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 模拟训练过程
        /// </summary>
        private async Task SimulateTraining(int epochs, CancellationToken cancellationToken)
        {
            for (int epoch = 1; epoch <= epochs; epoch++)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 更新当前轮次
                CurrentEpochText.Text = $"{epoch}/{epochs}";

                // 模拟每个轮次的训练
                for (int step = 0; step < 100; step++)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 更新进度
                    var epochProgress = (step + 1) * 100 / 100;
                    var overallProgress = ((epoch - 1) * 100 + epochProgress) * 100 / (epochs * 100);

                    EpochProgressBar.Value = epochProgress;
                    EpochProgressText.Text = $"{epochProgress}%";
                    OverallProgressBar.Value = overallProgress;
                    OverallProgressText.Text = $"{overallProgress:F1}%";

                    // 模拟损失和准确率变化
                    var trainingLoss = 2.0 - (epoch * 1.8 / epochs) + (new Random().NextDouble() - 0.5) * 0.1;
                    var validationLoss = 2.2 - (epoch * 1.9 / epochs) + (new Random().NextDouble() - 0.5) * 0.15;
                    var accuracy = (epoch * 85.0 / epochs) + (new Random().NextDouble() - 0.5) * 5;

                    TrainingLossText.Text = $"{Math.Max(0, trainingLoss):F3}";
                    ValidationLossText.Text = $"{Math.Max(0, validationLoss):F3}";
                    AccuracyText.Text = $"{Math.Max(0, Math.Min(100, accuracy)):F2}%";

                    UpdateTrainingStatus($"训练中 - 轮次 {epoch}/{epochs}", Brushes.Blue);

                    await Task.Delay(50, cancellationToken); // 模拟训练时间
                }

                // 轮次完成后稍作停顿
                await Task.Delay(200, cancellationToken);
            }
        }

        /// <summary>
        /// 暂停训练
        /// </summary>
        private void PauseTraining_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("暂停训练功能正在开发中。", "提示",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 停止训练
        /// </summary>
        private void StopTraining_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要停止训练吗？当前进度将会丢失。", "确认",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _trainingCancellationTokenSource?.Cancel();
                UpdateTrainingStatus("正在停止训练...", Brushes.Orange);
            }
        }

        /// <summary>
        /// 查看日志
        /// </summary>
        private void ViewLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logWindow = new Window
                {
                    Title = "训练日志",
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this),
                    Content = new ScrollViewer
                    {
                        Content = new TextBlock
                        {
                            Text = GenerateSampleLog(),
                            Margin = new Thickness(16),
                            FontFamily = new FontFamily("Consolas"),
                            FontSize = 12
                        }
                    }
                };
                logWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查看日志时发生错误");
                MessageBox.Show($"无法打开日志窗口：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 生成示例日志
        /// </summary>
        private string GenerateSampleLog()
        {
            return $@"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 开始训练会话
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 模型架构: YOLOv11s
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 数据集: 医学影像数据集 v1.0
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 训练参数:
  - 轮数: 100
  - 批次大小: 8
  - 学习率: 0.001
  - 图像尺寸: 640x640
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 加载预训练权重
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 数据集验证完成
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 开始训练...
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 1/100 - Loss: 2.145, Val_Loss: 2.234, Accuracy: 12.5%
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 2/100 - Loss: 1.987, Val_Loss: 2.156, Accuracy: 18.3%
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: Epoch 3/100 - Loss: 1.823, Val_Loss: 2.089, Accuracy: 24.7%
...
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 训练完成
[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] INFO: 最佳模型已保存";
        }

        /// <summary>
        /// 训练模型列表选择变化
        /// </summary>
        private void TrainedModelsListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 当选择模型时可以显示更多详细信息
        }

        /// <summary>
        /// 测试模型
        /// </summary>
        private void TestModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                MessageBox.Show($"模型测试功能正在开发中。\n\n选中模型: {model.Name}\n准确率: {model.Accuracy:F2}%",
                              "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("请先选择要测试的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 导出模型
        /// </summary>
        private void ExportModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出模型",
                    Filter = "PyTorch 模型 (*.pt)|*.pt|ONNX 模型 (*.onnx)|*.onnx|所有文件 (*.*)|*.*",
                    FileName = model.Name,
                    DefaultExt = "pt"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    try
                    {
                        // 模拟导出过程
                        MessageBox.Show($"模型已导出到: {saveFileDialog.FileName}", "导出成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "导出模型失败");
                        MessageBox.Show($"导出模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要导出的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 部署模型
        /// </summary>
        private void DeployModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var result = MessageBox.Show($"确定要部署模型 '{model.Name}' 吗？\n\n这将替换当前的生产模型。",
                                           "确认部署", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // 模拟部署过程
                        MessageBox.Show($"模型 '{model.Name}' 已成功部署到生产环境！", "部署成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "部署模型失败");
                        MessageBox.Show($"部署模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要部署的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 删除模型
        /// </summary>
        private void DeleteModel_Click(object sender, RoutedEventArgs e)
        {
            if (TrainedModelsListView.SelectedItem is TrainedModel model)
            {
                var result = MessageBox.Show($"确定要删除模型 '{model.Name}' 吗？\n\n此操作无法撤销。",
                                           "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        _trainedModels.Remove(model);
                        MessageBox.Show($"模型 '{model.Name}' 已删除。", "删除成功",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "删除模型失败");
                        MessageBox.Show($"删除模型失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的模型。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 更新训练状态
        /// </summary>
        private void UpdateTrainingStatus(string status, System.Windows.Media.Brush color)
        {
            TrainingStatusText.Text = status;
            TrainingStatusIcon.Foreground = color;
        }

        /// <summary>
        /// 开始YOLO训练
        /// </summary>
        private async Task StartYoloTraining(CancellationToken cancellationToken)
        {
            try
            {
                // 获取YOLO训练参数
                var epochs = (int)EpochsSlider.Value;
                var batchSize = (int)BatchSizeSlider.Value;
                var learningRate = LearningRateSlider.Value;
                var modelArchitecture = ModelArchitectureComboBox.Text;

                _logger.LogInformation("开始YOLO训练 - 模型: {Model}, 轮数: {Epochs}, 批次: {BatchSize}, 学习率: {LearningRate}",
                                     modelArchitecture, epochs, batchSize, learningRate);

                // 模拟训练过程（实际应用中应该调用YoloService）
                await SimulateTraining(epochs, cancellationToken);

                if (!cancellationToken.IsCancellationRequested)
                {
                    // 训练完成，添加新模型
                    var newModel = new TrainedModel
                    {
                        Name = $"YOLOv11_Custom_{DateTime.Now:yyyyMMdd_HHmmss}",
                        Accuracy = 85.0 + new Random().NextDouble() * 10, // 模拟准确率
                        CreatedTime = DateTime.Now,
                        ModelPath = $"./models/yolov11_custom_{DateTime.Now:yyyyMMdd_HHmmss}.pt"
                    };

                    _trainedModels.Add(newModel);
                    UpdateTrainingStatus("YOLO训练完成！", Brushes.Green);

                    MessageBox.Show($"YOLO模型训练完成！\n\n模型名称: {newModel.Name}\n准确率: {newModel.Accuracy:F2}%",
                                  "训练完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "YOLO训练过程中发生异常");
                UpdateTrainingStatus($"YOLO训练失败: {ex.Message}", Brushes.Red);
                MessageBox.Show($"YOLO训练失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 开始nnUNet训练
        /// </summary>
        private async Task StartNnUNetTraining(CancellationToken cancellationToken)
        {
            try
            {
                if (_nnunetService == null)
                {
                    MessageBox.Show("nnUNet服务未可用，请检查环境配置。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 获取nnUNet训练参数
                var datasetId = int.TryParse(DatasetIdTextBox.Text, out var id) ? id : 1;
                var datasetName = DatasetNameTextBox.Text;
                var epochs = (int)NnUNetEpochsSlider.Value;
                var batchSize = (int)NnUNetBatchSizeSlider.Value;
                var learningRate = NnUNetLearningRateSlider.Value;
                var fold = (int)NnUNetFoldSlider.Value;
                var architecture = GetSelectedNnUNetArchitecture();

                _logger.LogInformation("开始nnUNet训练 - 数据集: {Dataset}, 架构: {Architecture}, 轮数: {Epochs}",
                                     datasetName, architecture, epochs);

                // 创建nnUNet训练配置
                var trainingConfig = new NnUNetTrainingConfig
                {
                    DatasetId = datasetId,
                    DatasetName = datasetName,
                    DatasetPath = $"./data/nnUNet_raw/Dataset{datasetId:D3}_{datasetName}",
                    Architecture = architecture,
                    MaxEpochs = epochs,
                    BatchSize = batchSize,
                    LearningRate = learningRate,
                    Fold = fold,
                    OutputDirectory = "./output/nnunet",
                    ExperimentName = $"nnunet_{datasetName}_{DateTime.Now:yyyyMMdd_HHmmss}",
                    UseMixedPrecision = UseMixedPrecisionCheckBox.IsChecked ?? true,
                    EnableDataAugmentation = EnableDataAugmentationCheckBox.IsChecked ?? true,
                    UseDeepSupervision = UseDeepSupervisionCheckBox.IsChecked ?? true,
                    ValidationFrequency = (int)ValidationFrequencySlider.Value,
                    Device = DeviceComboBox.SelectedItem is ComboBoxItem deviceItem ? deviceItem.Tag?.ToString() ?? "cuda" : "cuda"
                };

                // 创建进度回调
                var progress = new Progress<NnUNetTrainingProgress>(p =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        OverallProgressBar.Value = p.ProgressPercentage;
                        CurrentEpochText.Text = $"轮次: {p.CurrentEpoch}/{p.TotalEpochs}";
                        TrainingLossText.Text = $"训练损失: {p.TrainingLoss:F4}";
                        ValidationLossText.Text = $"验证损失: {p.ValidationLoss:F4}";
                        UpdateTrainingStatus($"nnUNet训练中... Epoch {p.CurrentEpoch}/{p.TotalEpochs}", Brushes.Blue);
                    });
                });

                // 开始训练
                var result = await _nnunetService.TrainModelAsync(trainingConfig, progress, cancellationToken);

                if (result.Success && !cancellationToken.IsCancellationRequested)
                {
                    // 训练完成，添加新模型
                    var newModel = new TrainedModel
                    {
                        Name = $"nnUNet_{datasetName}_{DateTime.Now:yyyyMMdd_HHmmss}",
                        Accuracy = result.Metrics.BestDiceScore * 100, // 将Dice系数转换为百分比
                        CreatedTime = DateTime.Now,
                        ModelPath = result.BestModelPath
                    };

                    _trainedModels.Add(newModel);
                    UpdateTrainingStatus("nnUNet训练完成！", Brushes.Green);

                    MessageBox.Show($"nnUNet模型训练完成！\n\n模型名称: {newModel.Name}\nDice系数: {result.Metrics.BestDiceScore:F4}\n训练时间: {result.TrainingTime}",
                                  "训练完成", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else if (!result.Success)
                {
                    UpdateTrainingStatus($"nnUNet训练失败: {result.ErrorMessage}", Brushes.Red);
                    MessageBox.Show($"nnUNet训练失败: {result.ErrorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "nnUNet训练过程中发生异常");
                UpdateTrainingStatus($"nnUNet训练失败: {ex.Message}", Brushes.Red);
                MessageBox.Show($"nnUNet训练失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取选择的nnUNet架构
        /// </summary>
        private NnUNetArchitecture GetSelectedNnUNetArchitecture()
        {
            if (NnUNetArchitectureComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Tag?.ToString() switch
                {
                    "TwoD" => NnUNetArchitecture.TwoD,
                    "ThreeD_LowRes" => NnUNetArchitecture.ThreeD_LowRes,
                    "ThreeD_FullRes" => NnUNetArchitecture.ThreeD_FullRes,
                    "ThreeD_Cascade" => NnUNetArchitecture.ThreeD_Cascade,
                    _ => NnUNetArchitecture.ThreeD_FullRes
                };
            }
            return NnUNetArchitecture.ThreeD_FullRes;
        }
    }

    /// <summary>
    /// 训练模型数据模型
    /// </summary>
    public class TrainedModel
    {
        public string Name { get; set; } = "";
        public double Accuracy { get; set; }
        public DateTime CreatedTime { get; set; }
        public string ModelPath { get; set; } = "";
    }

    /// <summary>
    /// 数据集信息
    /// </summary>
    public class DatasetInfo
    {
        public int ImageCount { get; set; }
        public int LabelCount { get; set; }
        public int SubfolderCount { get; set; }
        public long TotalSize { get; set; }
    }
}
