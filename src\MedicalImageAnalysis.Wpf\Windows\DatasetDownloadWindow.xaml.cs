using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using MedicalImageAnalysis.Core.Models;
using MedicalImageAnalysis.Core.Services;

namespace MedicalImageAnalysis.Wpf.Windows
{
    /// <summary>
    /// DatasetDownloadWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DatasetDownloadWindow : Window
    {
        private readonly ILogger<DatasetDownloadWindow> _logger;
        private readonly OpenSourceDatasetService _datasetService;
        private List<OpenSourceDatasetViewModel> _datasets = new();
        private CancellationTokenSource? _downloadCancellationTokenSource;

        public DatasetDownloadWindow()
        {
            InitializeComponent();
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<DatasetDownloadWindow>.Instance;
            _datasetService = new OpenSourceDatasetService(Microsoft.Extensions.Logging.Abstractions.NullLogger<OpenSourceDatasetService>.Instance);
            
            Loaded += DatasetDownloadWindow_Loaded;
        }

        private async void DatasetDownloadWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDatasetsAsync();
        }

        /// <summary>
        /// 加载数据集列表
        /// </summary>
        private async Task LoadDatasetsAsync()
        {
            try
            {
                var datasets = await _datasetService.GetAvailableDatasetsAsync();
                _datasets = datasets.Select(d => new OpenSourceDatasetViewModel(d)).ToList();
                DatasetsItemsControl.ItemsSource = _datasets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载数据集列表失败");
                MessageBox.Show($"加载数据集列表失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 下载按钮点击事件
        /// </summary>
        private async void DownloadButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is OpenSourceDatasetViewModel datasetViewModel)
            {
                await DownloadDatasetAsync(datasetViewModel);
            }
        }

        /// <summary>
        /// 打开按钮点击事件
        /// </summary>
        private void OpenButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is OpenSourceDatasetViewModel datasetViewModel)
            {
                try
                {
                    if (Directory.Exists(datasetViewModel.LocalPath))
                    {
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = "explorer.exe",
                            Arguments = $"\"{datasetViewModel.LocalPath}\"",
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("数据集目录不存在", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "打开数据集目录失败");
                    MessageBox.Show($"打开数据集目录失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is OpenSourceDatasetViewModel datasetViewModel)
            {
                var result = MessageBox.Show(
                    $"确定要删除数据集 '{datasetViewModel.Name}' 吗？\n\n此操作将永久删除本地文件，无法恢复。",
                    "确认删除",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _datasetService.DeleteDatasetAsync(datasetViewModel.Id);
                        if (success)
                        {
                            datasetViewModel.IsDownloaded = false;
                            datasetViewModel.LocalPath = string.Empty;
                            MessageBox.Show("数据集已删除", "提示",
                                          MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("删除数据集失败", "错误",
                                          MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "删除数据集失败");
                        MessageBox.Show($"删除数据集失败：{ex.Message}", "错误",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 下载数据集
        /// </summary>
        private async Task DownloadDatasetAsync(OpenSourceDatasetViewModel datasetViewModel)
        {
            try
            {
                // 显示进度卡片
                ProgressCard.Visibility = Visibility.Visible;
                ProgressDatasetName.Text = datasetViewModel.Name;
                ProgressStatus.Text = "准备下载...";
                DownloadProgressBar.Value = 0;
                ProgressDetails.Text = "0 MB / 0 MB";
                ProgressSpeed.Text = "";
                ProgressETA.Text = "";

                // 创建取消令牌
                _downloadCancellationTokenSource = new CancellationTokenSource();

                // 创建进度报告器
                var progress = new Progress<DatasetDownloadProgress>(UpdateDownloadProgress);

                // 开始下载
                var success = await _datasetService.DownloadDatasetAsync(
                    datasetViewModel.Dataset, 
                    progress, 
                    _downloadCancellationTokenSource.Token);

                if (success)
                {
                    datasetViewModel.IsDownloaded = true;
                    datasetViewModel.LocalPath = datasetViewModel.Dataset.LocalPath;
                    
                    MessageBox.Show($"数据集 '{datasetViewModel.Name}' 下载完成！", "下载完成",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"数据集 '{datasetViewModel.Name}' 下载失败", "下载失败",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (OperationCanceledException)
            {
                MessageBox.Show("下载已取消", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载数据集失败");
                MessageBox.Show($"下载数据集失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏进度卡片
                ProgressCard.Visibility = Visibility.Collapsed;
                _downloadCancellationTokenSource?.Dispose();
                _downloadCancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 更新下载进度
        /// </summary>
        private void UpdateDownloadProgress(DatasetDownloadProgress progress)
        {
            Dispatcher.Invoke(() =>
            {
                ProgressStatus.Text = progress.Status switch
                {
                    "Preparing" => "准备中...",
                    "Downloading" => "下载中...",
                    "Extracting" => "解压中...",
                    "Completed" => "完成",
                    "Failed" => $"失败: {progress.ErrorMessage}",
                    _ => progress.Status
                };

                DownloadProgressBar.Value = progress.ProgressPercentage;

                var downloadedMB = progress.DownloadedBytes / 1024.0 / 1024.0;
                var totalMB = progress.TotalBytes / 1024.0 / 1024.0;
                ProgressDetails.Text = $"{downloadedMB:F1} MB / {totalMB:F1} MB";

                if (progress.DownloadSpeedBytesPerSecond > 0)
                {
                    var speedMBps = progress.DownloadSpeedBytesPerSecond / 1024.0 / 1024.0;
                    ProgressSpeed.Text = $"速度: {speedMBps:F1} MB/s";
                }

                if (progress.EstimatedTimeRemaining.TotalSeconds > 0)
                {
                    var eta = progress.EstimatedTimeRemaining;
                    if (eta.TotalHours >= 1)
                    {
                        ProgressETA.Text = $"剩余: {eta.Hours:D2}:{eta.Minutes:D2}:{eta.Seconds:D2}";
                    }
                    else
                    {
                        ProgressETA.Text = $"剩余: {eta.Minutes:D2}:{eta.Seconds:D2}";
                    }
                }
            });
        }

        protected override void OnClosed(EventArgs e)
        {
            _downloadCancellationTokenSource?.Cancel();
            _downloadCancellationTokenSource?.Dispose();
            _datasetService?.Dispose();
            base.OnClosed(e);
        }
    }

    /// <summary>
    /// 开源数据集视图模型
    /// </summary>
    public class OpenSourceDatasetViewModel
    {
        public OpenSourceDataset Dataset { get; }

        public OpenSourceDatasetViewModel(OpenSourceDataset dataset)
        {
            Dataset = dataset;
        }

        public string Id => Dataset.Id;
        public string Name => Dataset.Name;
        public string Description => Dataset.Description;
        public string Category => Dataset.Category;
        public string DatasetType => Dataset.DatasetType;
        public long SizeBytes => Dataset.SizeBytes;
        public int ImageCount => Dataset.ImageCount;
        public int AnnotationCount => Dataset.AnnotationCount;
        public List<string> Classes => Dataset.Classes;
        public string DownloadUrl => Dataset.DownloadUrl;
        public string License => Dataset.License;
        public string Author => Dataset.Author;
        public DateTime PublishDate => Dataset.PublishDate;
        public string Version => Dataset.Version;
        
        public bool IsDownloaded
        {
            get => Dataset.IsDownloaded;
            set => Dataset.IsDownloaded = value;
        }
        
        public string LocalPath
        {
            get => Dataset.LocalPath;
            set => Dataset.LocalPath = value;
        }

        public string SizeFormatted
        {
            get
            {
                string[] sizes = { "B", "KB", "MB", "GB", "TB" };
                double len = SizeBytes;
                int order = 0;
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                return $"{len:0.##} {sizes[order]}";
            }
        }
    }
}
